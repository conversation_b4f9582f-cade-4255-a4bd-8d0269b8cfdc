<template>
  <div class="modal fade" :id="modalId" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="fas fa-tags me-2"></i>
            {{ isEditing ? 'Editar Categoria' : 'Nova Categoria' }}
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        
        <form @submit.prevent="salvarCategoria">
          <div class="modal-body">
            <div class="row">
              <div class="col-12 mb-3">
                <label for="nome" class="form-label">Nome da Categoria *</label>
                <input
                  type="text"
                  id="nome"
                  v-model="formData.nome"
                  class="form-control"
                  :class="{ 'is-invalid': errors.nome }"
                  placeholder="Ex: Ortodontia, Limpeza, Cirurgia..."
                  required
                >
                <div v-if="errors.nome" class="invalid-feedback">{{ errors.nome }}</div>
              </div>

              <div class="col-12 mb-3">
                <label for="descricao" class="form-label">Descrição</label>
                <textarea
                  id="descricao"
                  v-model="formData.descricao"
                  class="form-control"
                  :class="{ 'is-invalid': errors.descricao }"
                  rows="3"
                  placeholder="Descrição opcional da categoria..."
                ></textarea>
                <div v-if="errors.descricao" class="invalid-feedback">{{ errors.descricao }}</div>
              </div>

              <div class="col-12 mb-3">
                <label for="cor" class="form-label">Cor da Categoria</label>
                <div class="d-flex align-items-center">
                  <input
                    type="color"
                    id="cor"
                    v-model="formData.cor"
                    class="form-control form-control-color me-3"
                    :class="{ 'is-invalid': errors.cor }"
                    style="width: 60px; height: 38px;"
                  >
                  <input
                    type="text"
                    v-model="formData.cor"
                    class="form-control"
                    :class="{ 'is-invalid': errors.cor }"
                    placeholder="#007bff"
                    pattern="^#[0-9A-Fa-f]{6}$"
                  >
                </div>
                <div v-if="errors.cor" class="invalid-feedback">{{ errors.cor }}</div>
                <small class="form-text text-muted">A cor será usada para identificar visualmente a categoria</small>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              <i class="fas fa-times me-1"></i>
              Cancelar
            </button>
            <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
              <span v-if="isSubmitting" class="spinner-border spinner-border-sm me-2" role="status"></span>
              <i v-else class="fas fa-save me-1"></i>
              {{ isSubmitting ? 'Salvando...' : 'Salvar' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import categoriaServicoService from '@/services/categoriaServicoService';
import cSwal from '@/utils/cSwal';
import { openModal, closeModal } from '@/utils/modalHelper';

export default {
  name: 'ModalCategoria',
  props: {
    modalId: {
      type: String,
      default: 'modalCategoria'
    }
  },
  data() {
    return {
      formData: {
        nome: '',
        descricao: '',
        cor: '#007bff'
      },
      errors: {},
      isSubmitting: false,
      isEditing: false,
      categoriaId: null
    };
  },
  methods: {
    abrirModal(categoria = null) {
      this.resetForm();
      
      if (categoria) {
        this.isEditing = true;
        this.categoriaId = categoria.id;
        this.formData = {
          nome: categoria.nome || '',
          descricao: categoria.descricao || '',
          cor: categoria.cor || '#007bff'
        };
      } else {
        this.isEditing = false;
        this.categoriaId = null;
      }

      // Abrir modal usando modalHelper
      openModal(this.modalId);
    },

    resetForm() {
      this.formData = {
        nome: '',
        descricao: '',
        cor: '#007bff'
      };
      this.errors = {};
      this.isSubmitting = false;
    },

    validarFormulario() {
      this.errors = {};
      let isValid = true;

      if (!this.formData.nome || this.formData.nome.trim() === '') {
        this.errors.nome = 'O nome da categoria é obrigatório';
        isValid = false;
      }

      if (this.formData.cor && !/^#[0-9A-Fa-f]{6}$/.test(this.formData.cor)) {
        this.errors.cor = 'Cor deve estar no formato hexadecimal (#000000)';
        isValid = false;
      }

      return isValid;
    },

    async salvarCategoria() {
      if (!this.validarFormulario()) {
        return;
      }

      this.isSubmitting = true;

      try {
        let response;
        
        if (this.isEditing) {
          response = await categoriaServicoService.atualizarCategoria(this.categoriaId, this.formData);
        } else {
          response = await categoriaServicoService.criarCategoria(this.formData);
        }

        if (response.data.success) {
          cSwal.cSuccess(response.data.message || 'Categoria salva com sucesso!');
          
          // Fechar modal
          closeModal(this.modalId);
          
          // Emitir evento para atualizar lista
          this.$emit('categoria-salva', response.data.data);
        } else {
          cSwal.cError(response.data.message || 'Erro ao salvar categoria');
        }
      } catch (error) {
        console.error('Erro ao salvar categoria:', error);
        
        if (error.response && error.response.data && error.response.data.data) {
          this.errors = error.response.data.data;
        } else {
          cSwal.cError('Erro ao salvar categoria: ' + (error.message || 'Erro desconhecido'));
        }
      } finally {
        this.isSubmitting = false;
      }
    }
  }
};
</script>

<style scoped>
.form-control-color {
  border-radius: 0.375rem;
  border: 1px solid #ced4da;
}

.form-control-color:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-header .btn-close {
  filter: invert(1);
}

.form-label {
  font-weight: 500;
  color: #495057;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
}
</style>
