<template>
  <div class="modal fade" :id="modalId" tabindex="-1" aria-hidden="true" :ref="modalId">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="fas fa-medical me-2"></i>
            {{ isEditing ? 'Editar Procedimento' : 'Novo Procedimento' }}
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        
        <form @submit.prevent="salvarProcedimento">
          <div class="modal-body">
            <div class="row">
              <!-- Informações Básicas -->
              <div class="col-md-8 mb-3">
                <label for="nome" class="form-label">Nome do Procedimento *</label>
                <input
                  type="text"
                  id="nome"
                  v-model="formData.nome"
                  class="form-control"
                  :class="{ 'is-invalid': errors.nome }"
                  placeholder="Ex: Limpeza Completa, Aparelho Ortodôntico..."
                  required
                >
                <div v-if="errors.nome" class="invalid-feedback">{{ errors.nome }}</div>
              </div>

              <div class="col-md-4 mb-3">
                <label for="codigo" class="form-label">Código</label>
                <input
                  type="text"
                  id="codigo"
                  v-model="formData.codigo"
                  class="form-control"
                  :class="{ 'is-invalid': errors.codigo }"
                  placeholder="Ex: LIMP001"
                >
                <div v-if="errors.codigo" class="invalid-feedback">{{ errors.codigo }}</div>
              </div>

              <div class="col-md-6 mb-3">
                <label for="categoria_id" class="form-label">Categoria</label>
                <select
                  id="categoria_id"
                  v-model="formData.categoria_id"
                  class="form-select"
                  :class="{ 'is-invalid': errors.categoria_id }"
                >
                  <option value="">Selecione uma categoria</option>
                  <option 
                    v-for="categoria in categorias" 
                    :key="categoria.id" 
                    :value="categoria.id"
                  >
                    {{ categoria.nome }}
                  </option>
                </select>
                <div v-if="errors.categoria_id" class="invalid-feedback">{{ errors.categoria_id }}</div>
              </div>

              <div class="col-md-6 mb-3">
                <label for="tipo" class="form-label">Tipo *</label>
                <select
                  id="tipo"
                  v-model="formData.tipo"
                  class="form-select"
                  :class="{ 'is-invalid': errors.tipo }"
                  required
                >
                  <option value="">Selecione o tipo</option>
                  <option value="servico">Serviço</option>
                  <option value="produto">Produto</option>
                  <option value="procedimento">Procedimento</option>
                </select>
                <div v-if="errors.tipo" class="invalid-feedback">{{ errors.tipo }}</div>
              </div>

              <div class="col-12 mb-3">
                <label for="descricao" class="form-label">Descrição</label>
                <textarea
                  id="descricao"
                  v-model="formData.descricao"
                  class="form-control"
                  :class="{ 'is-invalid': errors.descricao }"
                  rows="3"
                  placeholder="Descrição detalhada do procedimento..."
                ></textarea>
                <div v-if="errors.descricao" class="invalid-feedback">{{ errors.descricao }}</div>
              </div>

              <!-- Valores -->
              <div class="col-md-4 mb-3">
                <label for="valor_base" class="form-label">Valor Base *</label>
                <div class="input-group">
                  <span class="input-group-text">R$</span>
                  <input
                    type="number"
                    id="valor_base"
                    v-model="formData.valor_base"
                    class="form-control"
                    :class="{ 'is-invalid': errors.valor_base }"
                    step="0.01"
                    min="0"
                    placeholder="0,00"
                    required
                  >
                </div>
                <div v-if="errors.valor_base" class="invalid-feedback">{{ errors.valor_base }}</div>
              </div>

              <div class="col-md-4 mb-3">
                <label for="valor_minimo" class="form-label">Valor Mínimo</label>
                <div class="input-group">
                  <span class="input-group-text">R$</span>
                  <input
                    type="number"
                    id="valor_minimo"
                    v-model="formData.valor_minimo"
                    class="form-control"
                    :class="{ 'is-invalid': errors.valor_minimo }"
                    step="0.01"
                    min="0"
                    placeholder="0,00"
                  >
                </div>
                <div v-if="errors.valor_minimo" class="invalid-feedback">{{ errors.valor_minimo }}</div>
              </div>

              <div class="col-md-4 mb-3">
                <label for="valor_maximo" class="form-label">Valor Máximo</label>
                <div class="input-group">
                  <span class="input-group-text">R$</span>
                  <input
                    type="number"
                    id="valor_maximo"
                    v-model="formData.valor_maximo"
                    class="form-control"
                    :class="{ 'is-invalid': errors.valor_maximo }"
                    step="0.01"
                    min="0"
                    placeholder="0,00"
                  >
                </div>
                <div v-if="errors.valor_maximo" class="invalid-feedback">{{ errors.valor_maximo }}</div>
              </div>

              <!-- Informações Adicionais -->
              <div class="col-md-6 mb-3">
                <label for="unidade" class="form-label">Unidade</label>
                <input
                  type="text"
                  id="unidade"
                  v-model="formData.unidade"
                  class="form-control"
                  :class="{ 'is-invalid': errors.unidade }"
                  placeholder="Ex: un, kg, m²..."
                >
                <div v-if="errors.unidade" class="invalid-feedback">{{ errors.unidade }}</div>
              </div>

              <div class="col-md-6 mb-3">
                <label for="tempo_estimado" class="form-label">Tempo Estimado (minutos)</label>
                <input
                  type="number"
                  id="tempo_estimado"
                  v-model="formData.tempo_estimado"
                  class="form-control"
                  :class="{ 'is-invalid': errors.tempo_estimado }"
                  min="1"
                  placeholder="Ex: 30, 60, 120..."
                >
                <div v-if="errors.tempo_estimado" class="invalid-feedback">{{ errors.tempo_estimado }}</div>
              </div>

              <div class="col-12 mb-3">
                <label for="observacoes" class="form-label">Observações</label>
                <textarea
                  id="observacoes"
                  v-model="formData.observacoes"
                  class="form-control"
                  :class="{ 'is-invalid': errors.observacoes }"
                  rows="2"
                  placeholder="Observações adicionais..."
                ></textarea>
                <div v-if="errors.observacoes" class="invalid-feedback">{{ errors.observacoes }}</div>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              <i class="fas fa-times me-1"></i>
              Cancelar
            </button>
            <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
              <span v-if="isSubmitting" class="spinner-border spinner-border-sm me-2" role="status"></span>
              <i v-else class="fas fa-save me-1"></i>
              {{ isSubmitting ? 'Salvando...' : 'Salvar' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { servicoProdutoService } from '@/services/servicoProdutoService';
import categoriaServicoService from '@/services/categoriaServicoService';
import cSwal from '@/utils/cSwal';
// import { openModal, closeModal } from '@/utils/modalHelper';

export default {
  name: 'ModalProcedimento',
  props: {
    modalId: {
      type: String,
      default: 'modalProcedimento'
    }
  },
  data() {
    return {
      formData: {
        nome: '',
        codigo: '',
        categoria_id: '',
        tipo: '',
        descricao: '',
        valor_base: '',
        valor_minimo: '',
        valor_maximo: '',
        unidade: 'un',
        tempo_estimado: '',
        observacoes: ''
      },
      errors: {},
      isSubmitting: false,
      isEditing: false,
      procedimentoId: null,
      categorias: []
    };
  },
  async created() {
    await this.carregarCategorias();
  },
  mounted() {
    // Escutar evento de fechamento do modal
    const modalElement = this.$refs[this.modalId];
    if (modalElement) {
      modalElement.addEventListener('hidden.bs.modal', () => {
        this.resetForm();
      });
    }
  },
  methods: {
    async carregarCategorias() {
      try {
        const response = await categoriaServicoService.getCategoriasAtivas();
        if (response.data.success) {
          this.categorias = response.data.data;
        }
      } catch (error) {
        console.error('Erro ao carregar categorias:', error);
      }
    },

    abrirModal(procedimento = null) {
      this.resetForm();

      if (procedimento) {
        this.isEditing = true;
        this.procedimentoId = procedimento.id;
        this.formData = {
          nome: procedimento.nome || '',
          codigo: procedimento.codigo || '',
          categoria_id: procedimento.categoria_id || '',
          tipo: procedimento.tipo || '',
          descricao: procedimento.descricao || '',
          valor_base: procedimento.valor_base || '',
          valor_minimo: procedimento.valor_minimo || '',
          valor_maximo: procedimento.valor_maximo || '',
          unidade: procedimento.unidade || 'un',
          tempo_estimado: procedimento.tempo_estimado || '',
          observacoes: procedimento.observacoes || ''
        };
      } else {
        this.isEditing = false;
        this.procedimentoId = null;
      }

      // Aguardar próximo tick para garantir que o DOM foi atualizado
      this.$nextTick(() => {
        // Limpar qualquer backdrop existente antes de abrir
        this.limparBackdrops();

        // Método alternativo mais direto para abrir o modal
        const modalElement = document.getElementById(this.modalId);
        if (modalElement) {
          modalElement.classList.add('show');
          modalElement.style.display = 'block';
          modalElement.setAttribute('aria-modal', 'true');
          modalElement.removeAttribute('aria-hidden');

          // Adicionar backdrop
          const backdrop = document.createElement('div');
          backdrop.className = 'modal-backdrop fade show';
          document.body.appendChild(backdrop);

          // Adicionar classe ao body
          document.body.classList.add('modal-open');
        }
      });
    },

    resetForm() {
      this.formData = {
        nome: '',
        codigo: '',
        categoria_id: '',
        tipo: '',
        descricao: '',
        valor_base: '',
        valor_minimo: '',
        valor_maximo: '',
        unidade: 'un',
        tempo_estimado: '',
        observacoes: ''
      };
      this.errors = {};
      this.isSubmitting = false;
    },

    validarFormulario() {
      this.errors = {};
      let isValid = true;

      if (!this.formData.nome || this.formData.nome.trim() === '') {
        this.errors.nome = 'O nome do procedimento é obrigatório';
        isValid = false;
      }

      if (!this.formData.tipo) {
        this.errors.tipo = 'O tipo é obrigatório';
        isValid = false;
      }

      if (!this.formData.valor_base || this.formData.valor_base <= 0) {
        this.errors.valor_base = 'O valor base é obrigatório e deve ser maior que zero';
        isValid = false;
      }

      if (this.formData.valor_minimo && this.formData.valor_maximo) {
        if (parseFloat(this.formData.valor_minimo) > parseFloat(this.formData.valor_maximo)) {
          this.errors.valor_minimo = 'O valor mínimo não pode ser maior que o valor máximo';
          isValid = false;
        }
      }

      return isValid;
    },

    async salvarProcedimento() {
      if (!this.validarFormulario()) {
        return;
      }

      this.isSubmitting = true;

      try {
        let response;
        
        if (this.isEditing) {
          response = await servicoProdutoService.atualizarServicoProduto(this.procedimentoId, this.formData);
        } else {
          response = await servicoProdutoService.criarServicoProduto(this.formData);
        }

        if (response.data.success) {
          cSwal.cSuccess(response.data.message || 'Procedimento salvo com sucesso!');
          
          // Fechar modal manualmente
          this.fecharModal();
          
          // Emitir evento para atualizar lista
          this.$emit('procedimento-salvo', response.data.data);
        } else {
          cSwal.cError(response.data.message || 'Erro ao salvar procedimento');
        }
      } catch (error) {
        console.error('Erro ao salvar procedimento:', error);
        
        if (error.response && error.response.data && error.response.data.data) {
          this.errors = error.response.data.data;
        } else {
          cSwal.cError('Erro ao salvar procedimento: ' + (error.message || 'Erro desconhecido'));
        }
      } finally {
        this.isSubmitting = false;
      }
    },

    limparBackdrops() {
      // Remove qualquer backdrop órfão que possa estar causando problemas
      const backdrops = document.querySelectorAll('.modal-backdrop');
      backdrops.forEach(backdrop => {
        backdrop.remove();
      });

      // Remove classe modal-open do body se não há modais abertos
      const modalsAbertos = document.querySelectorAll('.modal.show');
      if (modalsAbertos.length === 0) {
        document.body.classList.remove('modal-open');
        document.body.style.paddingRight = '';
      }
    },

    fecharModal() {
      const modalElement = document.getElementById(this.modalId);
      if (modalElement) {
        modalElement.classList.remove('show');
        modalElement.style.display = 'none';
        modalElement.setAttribute('aria-hidden', 'true');
        modalElement.removeAttribute('aria-modal');
      }

      // Limpar backdrops
      this.limparBackdrops();
    }
  }
};
</script>

<style scoped>
/* Garantir z-index correto para o modal */
:deep(.modal) {
  z-index: 1055 !important;
}

:deep(.modal-backdrop) {
  z-index: 1050 !important;
}

.modal-header {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.modal-header .btn-close {
  filter: invert(1);
}

.form-label {
  font-weight: 500;
  color: #495057;
}

.btn-primary {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
  transform: translateY(-1px);
}

.input-group-text {
  background-color: #f8f9fa;
  border-color: #ced4da;
}
</style>
